import tkinter as tk
from tkinter import ttk, messagebox, Toplevel, filedialog
import ttkbootstrap as bs
from ttkbootstrap.constants import *
import pandas as pd
from datetime import datetime
import os
import json

class SettingsWindow(Toplevel):
    """
    Fenêtre pour gérer les profils des donneurs d'ordre (payeurs).
    """
    def __init__(self, parent):
        super().__init__(parent)
        self.parent = parent
        self.title("Gérer les Profils Donneur d'ordre")
        self.geometry("700x550")
        self.transient(parent)
        self.grab_set()

        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=BOTH, expand=YES)

        list_frame = ttk.Labelframe(main_frame, text="Profils Existants", padding="10")
        list_frame.pack(fill=BOTH, expand=YES, padx=5, pady=5)
        
        self.profiles_listbox = tk.Listbox(list_frame, height=10)
        self.profiles_listbox.pack(fill=BOTH, expand=YES)
        self.profiles_listbox.bind('<<ListboxSelect>>', self.on_profile_select)

        details_frame = ttk.Labelframe(main_frame, text="Détails du Profil", padding="10")
        details_frame.pack(fill=X, padx=5, pady=5)

        self.nom_var = tk.StringVar()
        self.rib_var = tk.StringVar()
        self.adresse_var = tk.StringVar()
        self.banque_var = tk.StringVar()
        self.suffixe_var = tk.StringVar()

        self.create_form_entry(details_frame, "Nom ou Raison Sociale:", self.nom_var, 0)
        self.create_form_entry(details_frame, "RIB (20 chiffres):", self.rib_var, 1)
        self.create_form_entry(details_frame, "Adresse:", self.adresse_var, 2)
        
        ttk.Label(details_frame, text="Banque:").grid(row=3, column=0, sticky="w", padx=5, pady=5)
        bank_display_list = [f"{code} - {name}" for code, name in self.parent.bank_list.items()]
        self.banque_combo = ttk.Combobox(details_frame, textvariable=self.banque_var, values=bank_display_list, state="readonly")
        self.banque_combo.grid(row=3, column=1, sticky="ew", padx=5, pady=5)

        self.create_form_entry(details_frame, "Suffixe Numéro d'ordre:", self.suffixe_var, 4)

        action_frame = ttk.Frame(main_frame)
        action_frame.pack(fill=X, pady=10)

        ttk.Button(action_frame, text="Nouveau", command=self.clear_fields, bootstyle="info").pack(side=LEFT, padx=5, fill=X, expand=YES)
        ttk.Button(action_frame, text="Enregistrer", command=self.save_profile, bootstyle="success").pack(side=LEFT, padx=5, fill=X, expand=YES)
        ttk.Button(action_frame, text="Supprimer", command=self.delete_profile, bootstyle="danger").pack(side=LEFT, padx=5, fill=X, expand=YES)

        self.load_profiles_to_listbox()

    def create_form_entry(self, parent, label_text, var, row):
        ttk.Label(parent, text=label_text).grid(row=row, column=0, sticky="w", padx=5, pady=5)
        entry = ttk.Entry(parent, textvariable=var)
        entry.grid(row=row, column=1, sticky="ew", padx=5, pady=5)
        parent.grid_columnconfigure(1, weight=1)

    def load_profiles_to_listbox(self):
        self.profiles_listbox.delete(0, END)
        for profile in self.parent.settings['payers']:
            self.profiles_listbox.insert(END, f"{profile['name']} ({profile['rib']})")

    def on_profile_select(self, event=None):
        selection_indices = self.profiles_listbox.curselection()
        if not selection_indices:
            return
        
        selected_index = selection_indices[0]
        profile = self.parent.settings['payers'][selected_index]

        self.nom_var.set(profile.get('name', ''))
        self.rib_var.set(profile.get('rib', ''))
        self.adresse_var.set(profile.get('address', ''))
        self.suffixe_var.set(profile.get('suffix', '1221'))
        bank_display = f"{profile.get('bank_code', '')} - {self.parent.bank_list.get(profile.get('bank_code', ''), '')}"
        self.banque_var.set(bank_display)

    def clear_fields(self):
        self.nom_var.set("")
        self.rib_var.set("")
        self.adresse_var.set("")
        self.banque_var.set("")
        self.suffixe_var.set("1221")
        self.profiles_listbox.selection_clear(0, END)

    def save_profile(self):
        rib = self.rib_var.get()
        if not (rib and rib.isdigit() and len(rib) == 20):
            messagebox.showerror("Erreur", "Le RIB doit contenir 20 chiffres.", parent=self)
            return
        
        bank_code = self.banque_var.get().split(' - ')[0]
        
        new_profile_data = {
            "name": self.nom_var.get(),
            "rib": rib,
            "address": self.adresse_var.get(),
            "bank_code": bank_code,
            "suffix": self.suffixe_var.get()
        }

        existing_profile_index = -1
        for i, p in enumerate(self.parent.settings['payers']):
            if p['rib'] == rib:
                existing_profile_index = i
                break
        
        if existing_profile_index != -1:
            self.parent.settings['payers'][existing_profile_index] = new_profile_data
        else:
            self.parent.settings['payers'].append(new_profile_data)
        
        self.parent.save_settings()
        self.load_profiles_to_listbox()
        self.parent.update_profiles_combobox()
        messagebox.showinfo("Succès", "Profil enregistré avec succès.", parent=self)

    def delete_profile(self):
        selection_indices = self.profiles_listbox.curselection()
        if not selection_indices:
            messagebox.showwarning("Aucune sélection", "Veuillez sélectionner un profil à supprimer.", parent=self)
            return

        selected_index = selection_indices[0]
        profile_to_delete = self.parent.settings['payers'][selected_index]

        if messagebox.askyesno("Confirmation", f"Êtes-vous sûr de vouloir supprimer le profil pour {profile_to_delete['name']}?", parent=self):
            del self.parent.settings['payers'][selected_index]
            self.parent.save_settings()
            self.load_profiles_to_listbox()
            self.clear_fields()
            self.parent.update_profiles_combobox()

class VirementApp(bs.Window):
    def __init__(self):
        super().__init__(themename="litera", title="Générateur de Fichier de Virement Bancaire V1.0")
        self.geometry("900x550")
        self.minsize(800, 500)
        self.settings_file = "settings.json"
        self.settings = {}

        self.bank_list = {
            "001": "Banque Nationale d'Algerie", "002": "Banque Exterieur d'Algerie",
            "003": "Banque Algerienne Developp. Rural", "004": "CREDIT POPULAIRE D'ALGERIE",
            "005": "Banque Developpement Local", "006": "El Baraka",
            "007": "Centre des Cheques Postaux", "008": "Trésor Central", "010": "CNMA",
            "011": "CNEP", "012": "CITY BANK", "014": "A B C CORP.", "020": "NATEXIS",
            "021": "SOCIETE GENERALE Algerie", "026": "ARAB BANK PLC", "027": "B N P",
            "029": "TRUST BANK", "031": "HOUSING BANK AG", "032": "ALGERIA GULF BANK",
            "035": "FRANSABANK", "036": "CALYON", "037": "HSBC ALGERIA",
            "038": "AL SALAM BANK ALGERIA", "111": "Banque d'Algerie"
        }
        self.excel_file_path = tk.StringVar()

        self.load_settings()
        self.create_widgets()

    def create_widgets(self):
        main_frame = ttk.Frame(self, padding="15")
        main_frame.pack(fill=X, expand=NO, side=TOP)

        donneur_frame = ttk.Labelframe(main_frame, text=" 1. Informations du Donneur d'ordre ", padding="15")
        donneur_frame.pack(fill=X, expand=NO, pady=5)

        ttk.Label(donneur_frame, text="Choisir un profil:").grid(row=0, column=0, sticky="w", padx=5, pady=10)
        self.profiles_combo = ttk.Combobox(donneur_frame, state="readonly", width=50)
        self.profiles_combo.grid(row=0, column=1, sticky="ew", padx=5, pady=10)
        
        ttk.Button(donneur_frame, text="Gérer les profils...", command=self.open_settings, bootstyle="secondary-outline").grid(row=0, column=2, padx=10)
        donneur_frame.grid_columnconfigure(1, weight=1)

        remise_frame = ttk.Labelframe(main_frame, text=" 2. Informations sur la Remise ", padding="15")
        remise_frame.pack(fill=X, expand=NO, pady=10)

        ttk.Label(remise_frame, text="Date de la remise:").grid(row=0, column=0, sticky="w", padx=5, pady=5)
        self.date_remise_entry = bs.DateEntry(remise_frame, bootstyle="primary", dateformat="%d/%m/%Y", firstweekday=6)
        self.date_remise_entry.grid(row=0, column=1, sticky="ew", padx=5, pady=5)

        self.libelle_operation = self.create_form_entry(remise_frame, "Libellé de l'opération:", 1, default_value="VIREMENT SALAIRE")
        remise_frame.grid_columnconfigure(1, weight=1)

        benef_frame = ttk.Labelframe(main_frame, text=" 3. Fichier des Bénéficiaires ", padding="15")
        benef_frame.pack(fill=X, expand=NO, pady=10)
        ttk.Button(benef_frame, text="Sélectionner un fichier Excel...", command=self.select_excel_file, bootstyle="primary").pack(side=LEFT, padx=5)
        ttk.Entry(benef_frame, textvariable=self.excel_file_path, state="readonly").pack(side=LEFT, fill=X, expand=YES, padx=5)

        bottom_frame = ttk.Frame(self, padding="15")
        bottom_frame.pack(fill=X, side=BOTTOM)

        ttk.Button(bottom_frame, text="Générer le Fichier TXT", command=self.generate_file, bootstyle="success").pack(fill=X, ipady=10, pady=(0, 10))
        ttk.Label(bottom_frame, text="Développé et programmé pour le Comité de Participation de CP SARPI Spa par Z.BELBALI", anchor="center", font=("Helvetica", 10, "italic")).pack(fill=X)

        self.update_profiles_combobox()

    def create_form_entry(self, parent, label_text, row, default_value=""):
        ttk.Label(parent, text=label_text).grid(row=row, column=0, sticky="w", padx=5, pady=5)
        entry_var = tk.StringVar(value=default_value)
        entry = ttk.Entry(parent, textvariable=entry_var)
        entry.grid(row=row, column=1, sticky="ew", padx=5, pady=5)
        return entry_var

    def load_settings(self):
        try:
            with open(self.settings_file, 'r', encoding='utf-8') as f:
                self.settings = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            self.settings = {"last_reference": 0, "payers": []}
            self.save_settings()

    def save_settings(self):
        with open(self.settings_file, 'w', encoding='utf-8') as f:
            json.dump(self.settings, f, indent=4, ensure_ascii=False)
            
    def update_profiles_combobox(self):
        profile_names = [f"{p['name']} ({p['rib']})" for p in self.settings['payers']]
        self.profiles_combo['values'] = profile_names
        if profile_names:
            self.profiles_combo.current(0)

    def open_settings(self):
        SettingsWindow(self)

    def select_excel_file(self):
        file_path = filedialog.askopenfilename(
            title="Sélectionner le fichier Excel des bénéficiaires",
            filetypes=[("Fichiers Excel", "*.xlsx *.xls")]
        )
        if file_path:
            self.excel_file_path.set(file_path)

    def generate_file(self):
        selected_profile_index = self.profiles_combo.current()
        if selected_profile_index == -1:
            messagebox.showerror("Erreur", "Veuillez sélectionner ou créer un profil de donneur d'ordre.")
            return

        if not self.excel_file_path.get():
            messagebox.showerror("Erreur", "Veuillez sélectionner un fichier Excel.")
            return
            
        try:
            df = pd.read_excel(self.excel_file_path.get(), header=0)
            df['full_name'] = df['prenom'].astype(str).str.strip() + ' ' + df['nom'].astype(str).str.strip()
            df_beneficiaires = df[['full_name', 'rib', 'montant']].copy()
            beneficiaires = df_beneficiaires.to_dict('records')
            for b in beneficiaires:
                b['nom'] = b.pop('full_name')
        except KeyError as e:
            messagebox.showerror("Erreur de Fichier", f"Colonne manquante dans le fichier Excel: {e}\nAssurez-vous que les colonnes 'prenom', 'nom', 'rib', 'montant' existent.")
            return
        except Exception as e:
            messagebox.showerror("Erreur de Fichier", f"Impossible de lire le fichier Excel.\nErreur: {e}")
            return

        try:
            date_text = self.date_remise_entry.entry.get()
            date_obj = datetime.strptime(date_text, "%d/%m/%Y")
            date_remise_str = date_obj.strftime('%Y%m%d')
        except (ValueError, AttributeError):
            messagebox.showerror("Erreur", "La date entrée est invalide. Veuillez utiliser le format JJ/MM/AAAA.")
            return

        def format_text(value, length):
            return str(value).ljust(length)[:length]

        def format_number(value, length):
            return str(value).zfill(length)
        
        def format_amount(value, length):
            # Formate un nombre en chaîne de caractères avec des centimes, paddée par des zéros
            try:
                # S'assure que la valeur est un flottant, la formate avec 2 décimales, enlève le point.
                amount_str_in_cents = f"{float(value):.2f}".replace('.', '')
                return amount_str_in_cents.zfill(length)
            except (ValueError, TypeError):
                return "0" * length
        
        profile = self.settings['payers'][selected_profile_index]
        nom_donneur = profile['name']
        rib_donneur = profile['rib']
        adresse_donneur = profile['address']
        banque_donneur_code = profile['bank_code']
        suffixe_val = profile.get('suffix', '')

        # La référence est maintenant entièrement automatique
        ref_remise = self.settings.get("last_reference", 0) + 1
        ref_remise_str = f"{ref_remise:03d}"
        
        montant_total = pd.to_numeric(df_beneficiaires['montant'], errors='coerce').fillna(0).sum()
        nombre_operations = len(beneficiaires)

        entete = (format_text("VIRM", 4) +
                  format_number(banque_donneur_code, 3) +
                  format_text("010", 3) + "0" + "1" +
                  format_number(rib_donneur, 20) +
                  format_text("", 4) +
                  format_text(nom_donneur.upper(), 50) +
                  format_text(adresse_donneur.upper(), 70) +
                  format_text(date_remise_str, 8) +
                  format_number(ref_remise_str, 3) +
                  format_number(nombre_operations, 6) +
                  format_amount(montant_total, 16) +
                  format_text("", 31))

        lignes_fichier = [entete]
        
        for i, benef in enumerate(beneficiaires):
            if suffixe_val and len(suffixe_val) == 4 and suffixe_val.isdigit():
                suffixe_final = suffixe_val
            else:
                suffixe_final = date_obj.strftime("%m%y")
            
            num_ordre = f"{str(i+1).zfill(6)}{suffixe_final}"
            # Utiliser une version courte du nom du donneur pour l'adresse benef. comme dans l'exemple
            adresse_beneficiaire_text = nom_donneur.split(' ')[-1] # Ex: 'SARPI' de 'OEUVRES SOCIALES SARPI ALGER'
            adresse_beneficiaire = format_text(adresse_beneficiaire_text.upper(), 70)

            corps = (format_number(num_ordre, 10) + "1" +
                     format_number(str(benef['rib']), 20) +
                     format_text("", 4) +
                     format_text(benef['nom'].upper(), 50) +
                     adresse_beneficiaire +
                     format_amount(benef['montant'], 15) +
                     format_text(self.libelle_operation.get().upper(), 70) +
                     format_text("", 80))
            lignes_fichier.append(corps)

        fin = format_text("FVIR", 4) + format_text("", 96)
        lignes_fichier.append(fin)

        try:
            final_content = "\r\n".join(lignes_fichier) + "\r\n"
            output_path = filedialog.asksaveasfilename(
                title="Enregistrer le fichier de virement",
                defaultextension=".txt",
                filetypes=[("Fichiers Texte", "*.txt")],
                initialfile=f"Virement_{date_remise_str}_{ref_remise_str}.txt"
            )
            if output_path:
                with open(output_path, "w", encoding="latin-1", newline="") as f:
                    f.write(final_content)
                
                self.settings['last_reference'] = ref_remise
                self.save_settings()

                messagebox.showinfo("Succès", f"Le fichier de virement a été généré avec succès !\nEmplacement: {output_path}")
        except Exception as e:
            messagebox.showerror("Erreur d'Ecriture", f"Impossible de sauvegarder le fichier.\nErreur: {e}")

if __name__ == "__main__":
    app = VirementApp()
    app.mainloop()