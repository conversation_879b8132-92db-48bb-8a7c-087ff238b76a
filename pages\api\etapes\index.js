let stockageEtapes = {
  "1": [
    { date: "2025-06-01", description: "Offre envoyée à l'agence" },
    { date: "2025-06-05", description: "Réponse reçue de l'agence" }
  ],
  "2": [
    { date: "2025-06-02", description: "Entretien programmé" }
  ]
};

export default function handler(req, res) {
  if (req.method === "GET") {
    const offreId = req.query.offreId;
    const result = stockageEtapes[offreId] || [];
    return res.status(200).json(result);
  }

  if (req.method === "POST") {
    const { offreId, date, description } = req.body;

    if (!offreId || !date || !description) {
      return res.status(400).json({ message: "Champs requis manquants" });
    }

    if (!stockageEtapes[offreId]) {
      stockageEtapes[offreId] = [];
    }

    const nouvelleEtape = { date, description };
    stockageEtapes[offreId].push(nouvelleEtape);

    return res.status(201).json({ message: "Étape ajoutée", etape: nouvelleEtape });
  }

  res.setHeader("Allow", ["GET", "POST"]);
  res.status(405).end(`Méthode ${req.method} non autorisée`);
}
