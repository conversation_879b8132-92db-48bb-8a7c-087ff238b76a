// Page d'ajout d'une dérogation liée à plusieurs offres incomplètes
import React, { useEffect, useState, useContext } from "react";
import { useRouter } from "next/router";
import { AuthContext } from "../../contexts/AuthContext";

export default function DerogationPage() {
  const { currentUser } = useContext(AuthContext);
  const router = useRouter();

  const [offres, setOffres] = useState([]);
  const [form, setForm] = useState({ numero: "", date: "", envoyeur: "", offresLiees: [] });
  const [reponse, setReponse] = useState({ dateReponse: "", nombreAutorise: "" });
  const [showReponseFields, setShowReponseFields] = useState(false);

  useEffect(() => {
    if (!currentUser || (currentUser.role !== "admin" && currentUser.role !== "manager")) {
      router.push("/unauthorized");
    }
  }, [currentUser, router]);

  useEffect(() => {
    fetch("/api/offres/incompletes")
      .then((res) => res.json())
      .then((data) => setOffres(data))
      .catch((err) => console.error("Erreur chargement offres incomplètes:", err));
  }, []);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleReponseChange = (e) => {
    setReponse({ ...reponse, [e.target.name]: e.target.value });
  };

  const handleOffreSelect = (id) => {
    const updated = form.offresLiees.includes(id)
      ? form.offresLiees.filter((o) => o !== id)
      : [...form.offresLiees, id];
    setForm({ ...form, offresLiees: updated });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const payload = showReponseFields ? { ...form, reponse } : form;
      const res = await fetch("/api/derogations", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      if (!res.ok) throw new Error("Échec de l'envoi");
      const result = await res.json();
      alert("Dérogation enregistrée avec succès");
      setForm({ numero: "", date: "", envoyeur: "", offresLiees: [] });
      setReponse({ dateReponse: "", nombreAutorise: "" });
      setShowReponseFields(false);
    } catch (err) {
      console.error("Erreur enregistrement dérogation:", err);
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h1 className="text-xl font-bold text-blue-700 mb-4">📝 Ajouter une dérogation</h1>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block font-semibold">Numéro de dérogation</label>
          <input
            name="numero"
            value={form.numero}
            onChange={handleChange}
            className="border rounded px-3 py-1 w-full"
            placeholder="ex: D-2025-04"
            required
          />
        </div>

        <div>
          <label className="block font-semibold">Date de demande</label>
          <input
            type="date"
            name="date"
            value={form.date}
            onChange={handleChange}
            className="border rounded px-3 py-1 w-full"
            required
          />
        </div>

        <div>
          <label className="block font-semibold">Envoyé à</label>
          <select
            name="envoyeur"
            value={form.envoyeur}
            onChange={handleChange}
            className="border rounded px-3 py-1 w-full"
            required
          >
            <option value="">-- Choisir --</option>
            <option value="Directeur de l'emploi">Directeur de l'emploi</option>
            <option value="Wali">Wali</option>
          </select>
        </div>

        <div>
          <label className="block font-semibold mb-2">Offres concernées</label>
          <div className="border rounded p-2 max-h-40 overflow-y-auto">
            {offres.length === 0 ? (
              <p className="text-gray-500 italic">Aucune offre incomplète disponible</p>
            ) : (
              offres.map((offre) => (
                <label key={offre.id} className="block">
                  <input
                    type="checkbox"
                    checked={form.offresLiees.includes(offre.id)}
                    onChange={() => handleOffreSelect(offre.id)}
                    className="mr-2"
                  />
                  {offre.numero} – {offre.poste}
                </label>
              ))
            )}
          </div>
        </div>

        <div className="flex items-center gap-3">
          <input type="checkbox" checked={showReponseFields} onChange={() => setShowReponseFields(!showReponseFields)} />
          <label className="font-semibold">Ajouter la réponse du wali ou directeur</label>
        </div>

        {showReponseFields && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block font-semibold">Date de réponse</label>
              <input
                type="date"
                name="dateReponse"
                value={reponse.dateReponse}
                onChange={handleReponseChange}
                className="border rounded px-3 py-1 w-full"
              />
            </div>

            <div>
              <label className="block font-semibold">Nombre de postes autorisés</label>
              <input
                type="number"
                name="nombreAutorise"
                value={reponse.nombreAutorise}
                onChange={handleReponseChange}
                className="border rounded px-3 py-1 w-full"
              />
            </div>
          </div>
        )}

        <button
          type="submit"
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          ✅ Enregistrer la dérogation
        </button>
      </form>
    </div>
  );
}