// Ajouter une étape à une offre – formulaire dynamique
import React, { useState, useEffect, useContext } from "react";
import { useRouter } from "next/router";
import { AuthContext } from "../../contexts/AuthContext";

export default function AjouterEtapePage() {
  const { currentUser } = useContext(AuthContext);
  const router = useRouter();

  const [offres, setOffres] = useState([]);
  const [form, setForm] = useState({ offreId: "", date: "", description: "" });

  useEffect(() => {
    if (!currentUser || (currentUser.role !== "admin" && currentUser.role !== "manager")) {
      router.push("/unauthorized");
    }
  }, [currentUser, router]);

  useEffect(() => {
    fetch("/api/offres")
      .then((res) => res.json())
      .then((data) => setOffres(data))
      .catch((err) => console.error("Erreur chargement offres:", err));
  }, []);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      const res = await fetch("/api/etapes", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(form),
      });

      if (!res.ok) throw new Error("Erreur d'ajout de l'étape");

      const result = await res.json();
      console.log("Étape ajoutée:", result);
      setForm({ offreId: "", date: "", description: "" });
    } catch (err) {
      console.error("Erreur serveur:", err);
    }
  };

  return (
    <div className="p-6 max-w-lg mx-auto">
      <h1 className="text-xl font-bold text-blue-700 mb-4">🧩 Ajouter une étape à une offre</h1>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block font-semibold">Sélectionner une offre</label>
          <select
            name="offreId"
            value={form.offreId}
            onChange={handleChange}
            className="border rounded px-3 py-1 w-full"
            required
          >
            <option value="">-- Choisir --</option>
            {offres.map((offre) => (
              <option key={offre.id} value={offre.id}>
                {offre.numero} – {offre.poste}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block font-semibold">Date de l'étape</label>
          <input
            type="date"
            name="date"
            value={form.date}
            onChange={handleChange}
            className="border rounded px-3 py-1 w-full"
            required
          />
        </div>

        <div>
          <label className="block font-semibold">Description</label>
          <input
            name="description"
            value={form.description}
            onChange={handleChange}
            placeholder="ex: Réponse reçue de l'agence"
            className="border rounded px-3 py-1 w-full"
            required
          />
        </div>

        <button
          type="submit"
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          ➕ Ajouter l'étape
        </button>
      </form>
    </div>
  );
}
