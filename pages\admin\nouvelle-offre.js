// Nouvelle offre d'emploi – Ajout d'une offre avec contrôle de rôle
import React, { useState, useContext, useEffect } from "react";
import { useRouter } from "next/router";
import { AuthContext } from "../../../contexts/AuthContext"; // Chemin relatif mis à jour correctement

export default function NouvelleOffrePage() {
  const { currentUser } = useContext(AuthContext);
  const router = useRouter();

  const [numero, setNumero] = useState("");
  const [poste, setPoste] = useState("");
  const [date, setDate] = useState("");
  const [agence, setAgence] = useState("");
  const [nombrePostes, setNombrePostes] = useState("");

  useEffect(() => {
    // Protection avec délai pour laisser le temps à currentUser d'être défini
    const timeout = setTimeout(() => {
      if (!currentUser || (currentUser.role !== "admin" && currentUser.role !== "manager")) {
        router.push("/unauthorized");
      }
    }, 100);

    return () => clearTimeout(timeout);
  }, [currentUser, router]);

  if (!currentUser || (currentUser.role !== "admin" && currentUser.role !== "manager")) {
    return null;
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    const formData = { numero, poste, date, agence, nombrePostes };

    try {
      const response = await fetch("/api/offres", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error("Échec lors de l'enregistrement de l'offre.");
      }

      const result = await response.json();
      console.log("Offre enregistrée avec succès:", result);
      // Redirection ou notification ici si besoin

    } catch (error) {
      console.error("Erreur lors de l'envoi de l'offre:", error);
    }
  };

  return (
    <div className="p-6 max-w-xl mx-auto">
      <h1 className="text-xl font-bold text-blue-700 mb-4">📝 Nouvelle offre d'emploi</h1>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block font-semibold">Numéro de l'offre</label>
          <input
            name="numero"
            value={numero}
            onChange={(e) => setNumero(e.target.value)}
            placeholder="ex: 2025-003"
            className="border rounded px-3 py-1 w-full"
            required
          />
        </div>

        <div>
          <label className="block font-semibold">Poste</label>
          <input
            name="poste"
            value={poste}
            onChange={(e) => setPoste(e.target.value)}
            placeholder="ex: Technicien"
            className="border rounded px-3 py-1 w-full"
            required
          />
        </div>

        <div>
          <label className="block font-semibold">Date</label>
          <input
            type="date"
            name="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
            className="border rounded px-3 py-1 w-full"
            required
          />
        </div>

        <div>
          <label className="block font-semibold">Agence de destination</label>
          <input
            name="agence"
            value={agence}
            onChange={(e) => setAgence(e.target.value)}
            placeholder="ex: ALEM – Laghouat"
            className="border rounded px-3 py-1 w-full"
            required
          />
        </div>

        <div>
          <label className="block font-semibold">Nombre de postes demandés</label>
          <input
            name="nombrePostes"
            value={nombrePostes}
            onChange={(e) => setNombrePostes(e.target.value)}
            type="number"
            className="border rounded px-3 py-1 w-full"
            required
          />
        </div>

        <button
          type="submit"
          className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
        >
          ✅ Enregistrer l'offre
        </button>
      </form>
    </div>
  );
}
