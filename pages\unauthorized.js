// Page Unauthorized - Accès refusé
import React from "react";

export default function UnauthorizedPage() {
  const handleRedirect = () => {
    window.location.href = "/";
  };

  return (
    <div className="h-screen flex flex-col justify-center items-center text-center">
      <h1 className="text-3xl font-bold text-red-600 mb-4">🚫 Accès refusé</h1>
      <p className="text-lg mb-6">
        Vous n'avez pas les permissions nécessaires pour accéder à cette page.
      </p>
      <button
        onClick={handleRedirect}
        className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        Retour à l'accueil
      </button>
    </div>
  );
}
