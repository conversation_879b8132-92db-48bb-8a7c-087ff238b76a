// Suivi des étapes d'une offre – Table de progression dynamique
import React, { useEffect, useState, useContext } from "react";
import { useRouter } from "next/router";
import { AuthContext } from "../../contexts/AuthContext";

export default function SuiviEtapesPage() {
  const { currentUser } = useContext(AuthContext);
  const router = useRouter();
  const [offres, setOffres] = useState([]);
  const [etapes, setEtapes] = useState([]);
  const [selectedOffre, setSelectedOffre] = useState(null);

  useEffect(() => {
    if (!currentUser || (currentUser.role !== "admin" && currentUser.role !== "manager")) {
      router.push("/unauthorized");
    }
  }, [currentUser, router]);

  useEffect(() => {
    fetch("/api/offres")
      .then((res) => res.json())
      .then((data) => setOffres(data))
      .catch((err) => console.error("Erreur chargement offres:", err));
  }, []);

  const handleSelect = (offreId) => {
    setSelectedOffre(offreId);
    fetch(`/api/etapes?offreId=${offreId}`)
      .then((res) => res.json())
      .then((data) => setEtapes(data))
      .catch((err) => console.error("Erreur chargement étapes:", err));
  };

  return (
    <div className="p-6">
      <h1 className="text-xl font-bold text-blue-700 mb-4">📈 Suivi des étapes de l'offre</h1>

      <select
        onChange={(e) => handleSelect(e.target.value)}
        className="border rounded px-3 py-2 mb-4"
      >
        <option value="">Sélectionner une offre</option>
        {offres.map((offre) => (
          <option key={offre.id} value={offre.id}>
            {offre.numero} – {offre.poste}
          </option>
        ))}
      </select>

      {selectedOffre && (
        <div className="bg-white shadow rounded p-4">
          <h2 className="font-semibold text-lg mb-2">Étapes enregistrées</h2>
          <ul className="list-disc list-inside">
            {etapes.length > 0 ? (
              etapes.map((etape, index) => (
                <li key={index}>
                  {etape.date} – {etape.description}
                </li>
              ))
            ) : (
              <li>Aucune étape disponible pour cette offre.</li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
}
