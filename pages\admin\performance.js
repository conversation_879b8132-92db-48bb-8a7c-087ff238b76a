import React, { useContext } from "react";
import { AuthContext } from "../../contexts/AuthContext";

export default function PerformancePage() {
  const { currentUser } = useContext(AuthContext);

  if (currentUser.role !== "admin") {
    if (typeof window !== "undefined") {
      window.location.href = "/unauthorized";
    }
    return null;
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-blue-700 mb-6">📊 Tableau de bord</h1>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        <div className="bg-gray-100 rounded p-4">Total offres: <strong>25</strong></div>
        <div className="bg-gray-100 rounded p-4">Offres satisfaites: <strong>18</strong></div>
        <div className="bg-gray-100 rounded p-4">Documents: <strong>41</strong></div>
      </div>
    </div>
  );
}
