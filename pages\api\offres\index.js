// /pages/api/offres/index.js
export default function handler(req, res) {
  if (req.method === "GET") {
    // بيانات تجريبية
    const offres = [
      { id: "1", numero: "2025-001", poste: "Technicien" },
      { id: "2", numero: "2025-002", poste: "Magasinier" },
      { id: "3", numero: "2025-003", poste: "Comptable" }
    ];
    res.status(200).json(offres);
  } else {
    res.status(405).json({ message: "Méthode non autorisée" });
  }
}
