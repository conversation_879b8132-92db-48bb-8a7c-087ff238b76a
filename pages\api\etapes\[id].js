// /pages/api/etapes/[id].js
export default function handler(req, res) {
  const {
    query: { id },
    method
  } = req;

  if (method === "GET") {
    // بيانات تجريبية مرتبطة بـ id
    const mockEtapes = {
      "1": [
        { date: "2025-06-01", description: "Offre envoyée à l'agence" },
        { date: "2025-06-05", description: "Réponse reçue de l'agence" }
      ],
      "2": [
        { date: "2025-06-02", description: "Entretien programmé" },
        { date: "2025-06-06", description: "Résultats envoyés" }
      ],
      "3": []
    };

    const result = mockEtapes[id] || [];
    res.status(200).json(result);
  } else {
    res.status(405).json({ message: "Méthode non autorisée" });
  }
}
